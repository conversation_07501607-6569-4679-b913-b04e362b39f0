# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: x-k8s.io
layout:
- go.kubebuilder.io/v4
projectName: gateway-api-inference-extension
repo: sigs.k8s.io/gateway-api-inference-extension
resources:
- api:
    crdVersion: v1
    namespaced: true
  domain: x-k8s.io
  group: inference
  kind: InferencePool
  path: sigs.k8s.io/gateway-api-inference-extension/api/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  domain: x-k8s.io
  group: inference
  kind: InferenceModel
  path: sigs.k8s.io/gateway-api-inference-extension/api/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  domain: x-k8s.io
  group: inference
  kind: EndpointPickerConfig
  path: sigs.k8s.io/gateway-api-inference-extension/api/config/v1alpha1
  version: v1alpha1
version: "3"

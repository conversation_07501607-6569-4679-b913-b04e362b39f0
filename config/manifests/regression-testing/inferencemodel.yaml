apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-0
spec:
  modelName: adapter-0
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-0
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-1
spec:
  modelName: adapter-1
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-1
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-2
spec:
  modelName: adapter-2
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-2
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-3
spec:
  modelName: adapter-3
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-3
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-4
spec:
  modelName: adapter-4
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-4
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-5
spec:
  modelName: adapter-5
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-5
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-6
spec:
  modelName: adapter-6
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-6
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-7
spec:
  modelName: adapter-7
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-7
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-8
spec:
  modelName: adapter-8
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-8
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-9
spec:
  modelName: adapter-9
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-9
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-10
spec:
  modelName: adapter-10
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-10
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-11
spec:
  modelName: adapter-11
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-11
    weight: 100

---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-12
spec:
  modelName: adapter-12
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-12
    weight: 100


---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-13
spec:
  modelName: adapter-13
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-13
    weight: 100


---

apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: adapter-14
spec:
  modelName: adapter-14
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
  targetModels:
  - name: adapter-14
    weight: 100

---


apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: base-model
spec:
  modelName: meta-llama/Llama-3.1-8B-Instruct
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct
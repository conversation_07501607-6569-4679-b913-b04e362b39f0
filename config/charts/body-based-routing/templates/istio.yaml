{{- if eq .Values.provider.name "istio" }}
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: {{ .Values.bbr.name }}
  namespace: {{ .Release.Namespace }}
spec:
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      # context omitted so that this applies to both sidecars and gateways
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_FIRST
      value:
        name: envoy.filters.http.ext_proc
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
          failure_mode_allow: false
          allow_mode_override: true
          processing_mode:
            request_header_mode: "SEND"
            response_header_mode: "SKIP"
            request_body_mode: "FULL_DUPLEX_STREAMED"
            response_body_mode: "NONE"
            request_trailer_mode: "SEND"
            response_trailer_mode: "SKIP"
          grpc_service:
            envoy_grpc:
              cluster_name: outbound|{{ .Values.bbr.port }}||{{ .Values.bbr.name }}.{{ .Release.Namespace }}.svc.cluster.local
---
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: {{ .Values.bbr.name }}
  namespace: {{ .Release.Namespace }}
spec:
  host: {{ .Values.bbr.name }}.{{ .Release.Namespace }}.svc.cluster.local
  trafficPolicy:
      tls:
        mode: SIMPLE
        insecureSkipVerify: true
{{- end }}

inferenceExtension:
  replicas: 1
  image:
    name: epp
    hub: us-central1-docker.pkg.dev/k8s-staging-images/gateway-api-inference-extension
    tag: main
    pullPolicy: Always
  extProcPort: 9002
  env: {}
  enablePprof: true # Enable pprof handlers for profiling and debugging
  # This is the plugins configuration file. 
  pluginsConfigFile: "default-plugins.yaml"
  # pluginsCustomConfig:
  #   custom-plugins.yaml: |
  #     apiVersion: inference.networking.x-k8s.io/v1alpha1
  #     kind: EndpointPickerConfig
  #     plugins:
  #     - type: custom-scorer
  #       parameters:
  #         custom-threshold: 64
  #     - type: max-score-picker
  #     - type: single-profile-handler
  #     schedulingProfiles:
  #     - name: default
  #       plugins:
  #       - pluginRef: custom-scorer
  #         weight: 1
  #       - pluginRef: max-score-picker
  #         weight: 1

  # Example environment variables:
  # env:
  #   KV_CACHE_SCORE_WEIGHT: "1"

inferencePool:
  targetPortNumber: 8000
  modelServerType: vllm # vllm, triton-tensorrt-llm
  # modelServers: # REQUIRED
    # matchLabels: 
    #   app: vllm-llama3-8b-instruct

provider:
  name: none

gke:
  monitoringSecret:
    name: inference-gateway-sa-metrics-reader-secret
    namespace: default

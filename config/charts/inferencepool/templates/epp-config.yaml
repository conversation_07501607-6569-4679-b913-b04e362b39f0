apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "gateway-api-inference-extension.name" . }}
  namespace: {{ .Release.Namespace }}
data:
  default-plugins.yaml: |
    apiVersion: inference.networking.x-k8s.io/v1alpha1
    kind: EndpointPickerConfig
    plugins:
    - type: low-queue-filter
      parameters:
        threshold: 128
    - type: lora-affinity-filter
      parameters:
        threshold: 0.999
    - type: least-queue-filter
    - type: least-kv-cache-filter
    - type: decision-tree-filter
      name: low-latency-filter
      parameters:
        current:
          pluginRef: low-queue-filter
        nextOnSuccess:
          decisionTree:
            current:
              pluginRef: lora-affinity-filter
            nextOnSuccessOrFailure:
              decisionTree:
                current:
                  pluginRef: least-queue-filter
                nextOnSuccessOrFailure:
                  decisionTree:
                    current:
                      pluginRef: least-kv-cache-filter
        nextOnFailure:
          decisionTree:
            current:
              pluginRef: least-queue-filter
            nextOnSuccessOrFailure:
              decisionTree:
                current:
                  pluginRef: lora-affinity-filter
                nextOnSuccessOrFailure:
                  decisionTree:
                    current:
                      pluginRef: least-kv-cache-filter
    - type: random-picker
      parameters:
        maxNumOfEndpoints: 1
    - type: single-profile-handler
    schedulingProfiles:
    - name: default
      plugins:
      - pluginRef: low-latency-filter
      - pluginRef: random-picker
  plugins-v2.yaml: |
    apiVersion: inference.networking.x-k8s.io/v1alpha1
    kind: EndpointPickerConfig
    plugins:
    - type: queue-scorer
    - type: kv-cache-scorer
    - type: prefix-cache-scorer
      parameters:
        hashBlockSize: 64
        maxPrefixBlocksToMatch: 256
        lruCapacityPerServer: 31250
    - type: max-score-picker
      parameters:
        maxNumOfEndpoints: 1
    - type: single-profile-handler
    schedulingProfiles:
    - name: default
      plugins:
      - pluginRef: queue-scorer
        weight: 1
      - pluginRef: kv-cache-scorer
        weight: 1
      - pluginRef: prefix-cache-scorer
        weight: 1
      - pluginRef: max-score-picker
  {{- if (hasKey .Values.inferenceExtension "pluginsCustomConfig") }}
  {{- .Values.inferenceExtension.pluginsCustomConfig | toYaml | nindent 2 }}
  {{- end }}
  

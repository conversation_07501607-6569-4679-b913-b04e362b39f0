kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "gateway-api-inference-extension.name" . }}
  labels:
    {{- include "gateway-api-inference-extension.labels" . | nindent 4 }}
rules:
- apiGroups: ["inference.networking.x-k8s.io"]
  resources: ["inferencemodels", "inferencepools"]
  verbs: ["get", "watch", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "watch", "list"]
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "gateway-api-inference-extension.name" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "gateway-api-inference-extension.name" . }}
  namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "gateway-api-inference-extension.name" . }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "gateway-api-inference-extension.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway-api-inference-extension.labels" . | nindent 4 }}

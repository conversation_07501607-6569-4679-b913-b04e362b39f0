{{ include "gateway-api-inference-extension.validations.inferencepool.common" $ }}
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: {{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway-api-inference-extension.labels" . | nindent 4 }}
spec:
  targetPortNumber: {{ .Values.inferencePool.targetPortNumber }}
  selector:
    {{- if .Values.inferencePool.modelServers.matchLabels }}
    {{- range $key, $value := .Values.inferencePool.modelServers.matchLabels }}
    {{ $key }}: {{ quote $value }}
    {{- end }}
    {{- end }}
  extensionRef:
    name: {{ include "gateway-api-inference-extension.name" . }}

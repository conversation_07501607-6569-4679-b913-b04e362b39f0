version: 2
updates:
  # Maintain dependencies for go
  - package-ecosystem: "gomod"
    directory: "/"
    schedule:
      interval: "weekly"
    labels:
      - "area/dependency"
      - "ok-to-test"
      - "release-note-none"
    groups:
      kubernetes:
        patterns:
          - "k8s.io/*"
    ignore:
      # Ignore major and minor versions for dependencies updates
      # Allow patches and security updates.
      - dependency-name: k8s.io/*
        update-types: ["version-update:semver-major", "version-update:semver-minor"]

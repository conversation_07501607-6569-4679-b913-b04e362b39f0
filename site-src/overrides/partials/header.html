<!--
  Copyright (c) 2016-2023 <PERSON> <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->

<!-- Determine classes -->
{% set class = "md-header" %}
{% if "navigation.tabs.sticky" in features %}
  {% set class = class ~ " md-header--shadow md-header--lifted" %}
{% elif "navigation.tabs" not in features %}
  {% set class = class ~ " md-header--shadow" %}
{% endif %}

<!-- Header -->
<header class="{{ class }}" data-md-component="header">
  <nav
    class="md-header__inner md-grid"
    aria-label="{{ lang.t('header') }}"
  >

    <!-- Button to open drawer -->
    <label class="md-header__button md-icon" for="__drawer">
      {% set icon = config.theme.icon.menu or "material/menu" %}
      {% include ".icons/" ~ icon ~ ".svg" %}
    </label>

    <!-- Link to home -->
    <a
      href="{{ config.extra.homepage | d(nav.homepage.url, true) | url }}"
      title="{{ config.site_name | e }}"
      class="md-header__button md-logo"
      aria-label="{{ config.site_name }}"
      data-md-component="logo"
    >
      {% include "partials/logo.html" %}
    </a>

    <!-- Header title -->
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            {{ config.site_name }}
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            {% if page.meta and page.meta.title %}
              {{ page.meta.title }}
            {% else %}
              {{ page.title }}
            {% endif %}
          </span>
        </div>
      </div>
    </div>

    <!-- Color palette toggle -->
    {% if config.theme.palette %}
      {% if not config.theme.palette is mapping %}
        {% include "partials/palette.html" %}
      {% endif %}
    {% endif %}

    <!-- User preference: color palette -->
    {% if not config.theme.palette is mapping %}
      {% include "partials/javascripts/palette.html" %}
    {% endif %}

    <!-- Site language selector -->
    {% if config.extra.alternate %}
      {% include "partials/alternate.html" %}
    {% endif %}

    <!-- Button to open search modal -->
    {% if "material/search" in config.plugins %}
      <label class="md-header__button md-icon" for="__search">
        {% set icon = config.theme.icon.search or "material/magnify" %}
        {% include ".icons/" ~ icon ~ ".svg" %}
      </label>

      <!-- Search interface -->
      {% include "partials/search.html" %}
    {% endif %}

    <!-- Repository information -->
    {% if config.repo_url %}
      <div class="md-header__source">
        {% include "partials/source.html" %}
      </div>
    {% endif %}
  </nav>

  <!-- Navigation tabs (sticky) -->
  {% if "navigation.tabs.sticky" in features %}
    {% if "navigation.tabs" in features %}
      {% include "partials/tabs.html" %}
    {% endif %}
  {% endif %}
</header>

# GIEP-116: GIEP template

* Issue: [#0](https://github.com/kubernetes-sigs/gateway-api-inference-extension/issues/116)
* Status: Provisional|Implementable|Experimental|Standard|Deferred|Rejected|Withdrawn|Replaced

(See status definitions [here](overview.md#status).)

## TLDR

(1-2 sentence summary of the proposal)

## Goals

(Primary goals of this proposal.)

## Non-Goals

(What is out of scope for this proposal.)

## Introduction

(Can link to external doc -- but we should bias towards copying
the content into the GEP as online documents are easier to lose
-- e.g. owner messes up the permissions, accidental deletion)

## API

(... details, can point to PR with changes)

## Conformance Details

(This section describes the names to be used for the feature or
features in conformance tests and profiles.

These should be `CamelCase` names that specify the feature as
precisely as possible, and are particularly important for
Extended features, since they may be surfaced to users.)

## Alternatives

(List other design alternatives and why we did not go in that
direction)

## References

(Add any additional document links. Again, we should try to avoid
too much content not in version control to avoid broken links)

apiVersion: internal.gateway.networking.k8s.io/v1alpha1
kind: GIEPDetails
number: 116
name: GIEP template
status: Completed
# Any authors who contribute to the GEP in any way should be listed here using
# their Github handle.
authors:
  - robscott
relationships:
  # obsoletes indicates that a GEP makes the linked GEP obsolete, and completely
  # replaces that GEP. The obsoleted GEP MUST have its obsoletedBy field
  # set back to this GEP, and MUST be moved to Declined.
  obsoletes: {}
  obsoletedBy: {}
  # extends indicates that a GEP extends the linkned GEP, adding more detail
  # or additional implementation. The extended GEP MUST have its extendedBy
  # field set back to this GEP.
  extends: {}
  extendedBy: {}
  # seeAlso indicates other GEPs that are relevant in some way without being
  # covered by an existing relationship.
  seeAlso: {}
# references is a list of hyperlinks to relevant external references.
# It's intended to be used for storing Github discussions, Google docs, etc.
references: {}
# featureNames is a list of the feature names introduced by the GEP, if there
# are any. This will allow us to track which feature was introduced by which GEP.
featureNames: {}
# changelog is a list of hyperlinks to PRs that make changes to the GEP, in
# ascending date order.
changelog: {}

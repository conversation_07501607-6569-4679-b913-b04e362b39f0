# How to Get Involved

This page contains links to all of the meeting notes, design docs and related
discussions around the APIs.

## Bug Reports

Bug reports should be filed as [GitHub Issues](https://github.com/kubernetes-sigs/gateway-api-inference-extension/issues/new) on this repo.

**NOTE**: If you're reporting a bug that applies to a specific implementation of
this project, please check our
[implementations page](/implementations) to find links to the repositories where
you can get help with your specific implementation.

## Communications

Major discussions and notifications will be sent on both the
[WG-Serving](https://groups.google.com/a/kubernetes.io/g/wg-serving) and
[SIG-Network](https://groups.google.com/forum/#!forum/kubernetes-sig-network)
mailing lists.

Although we may end up creating a new Slack channel in the future, our
conversations are currently split between the following Kubernetes Slack
channels:

* [#sig-network-gateway-api](https://kubernetes.slack.com/archives/CR0H13KGA)
* [#wg-serving](https://kubernetes.slack.com/archives/C071WA7R9LY)

## Meetings

Gateway API community meetings happen every Thursday at 10am Pacific Time
([convert to your
timezone](https://dateful.com/time-zone-converter?t=10:00&tz=PT%20%28Pacific%20Time%29)).
To receive an invite to this and other WG-Serving community meetings, join the
[WG-Serving mailing
list](https://groups.google.com/a/kubernetes.io/g/wg-serving).

* [Zoom link](https://zoom.us/j/9955436256?pwd=Z2FQWU1jeDZkVC9RRTN4TlZyZTBHZz09) (passcode in [meeting notes](https://docs.google.com/document/d/1frfPE5L1sI3737rdQV04IcDGeOcGJj2ItjMg6z2SRH0/edit?tab=t.0#heading=h.jvz2pwvdpit0) doc)

### Meeting Notes and Recordings

Meeting agendas and notes are maintained in the [meeting
notes](https://docs.google.com/document/d/1frfPE5L1sI3737rdQV04IcDGeOcGJj2ItjMg6z2SRH0/edit?tab=t.0#heading=h.jvz2pwvdpit0)
doc. Feel free to add topics for discussion at an upcoming meeting.

All meetings are recorded and automatically uploaded to the [WG-Serving meetings
YouTube
playlist](https://www.youtube.com/playlist?list=PL69nYSiGNLP30qNanabU75ayPK7OPNAAS).

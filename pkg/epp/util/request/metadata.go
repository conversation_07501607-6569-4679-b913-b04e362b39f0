/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package request

import (
	extProcPb "github.com/envoyproxy/go-control-plane/envoy/service/ext_proc/v3"
)

func ExtractMetadataValues(req *extProcPb.ProcessingRequest) map[string]any {
	metadata := make(map[string]any)
	if req != nil && req.MetadataContext != nil && req.MetadataContext.FilterMetadata != nil {
		for key, val := range req.MetadataContext.FilterMetadata {
			metadata[key] = val.AsMap()
		}
	}
	return metadata
}

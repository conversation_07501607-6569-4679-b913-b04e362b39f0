# HELP inference_extension_scheduler_e2e_duration_seconds [ALPHA] End-to-end scheduling latency distribution in seconds.
# TYPE inference_extension_scheduler_e2e_duration_seconds histogram
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.0001"} 0
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.0002"} 1
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.0005"} 1
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.001"} 2
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.002"} 3
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.005"} 4
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.01"} 5
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.02"} 6
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.05"} 7
inference_extension_scheduler_e2e_duration_seconds_bucket{le="0.1"} 8
inference_extension_scheduler_e2e_duration_seconds_bucket{le="+Inf"} 9
inference_extension_scheduler_e2e_duration_seconds_sum{} 0.2835
inference_extension_scheduler_e2e_duration_seconds_count{} 9

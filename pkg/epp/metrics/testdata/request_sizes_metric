# HELP inference_model_request_sizes [ALPHA] Inference model requests size distribution in bytes for each model and target model.
# TYPE inference_model_request_sizes histogram
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="64"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="128"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="256"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="512"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="1024"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="2048"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="4096"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="8192"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="16384"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="32768"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="65536"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="131072"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="262144"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="524288"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="1.048576e+06"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="2.097152e+06"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="4.194304e+06"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="8.388608e+06"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="1.6777216e+07"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="3.3554432e+07"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="6.7108864e+07"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="1.34217728e+08"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="2.68435456e+08"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="5.36870912e+08"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="1.073741824e+09"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t10",le="+Inf"} 2
inference_model_request_sizes_sum{model_name="m10",target_model_name="t10"} 1700
inference_model_request_sizes_count{model_name="m10",target_model_name="t10"} 2
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="64"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="128"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="256"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="512"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="1024"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="2048"} 0
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="4096"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="8192"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="16384"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="32768"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="65536"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="131072"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="262144"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="524288"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="1.048576e+06"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="2.097152e+06"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="4.194304e+06"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="8.388608e+06"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="1.6777216e+07"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="3.3554432e+07"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="6.7108864e+07"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="1.34217728e+08"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="2.68435456e+08"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="5.36870912e+08"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="1.073741824e+09"} 1
inference_model_request_sizes_bucket{model_name="m10",target_model_name="t11",le="+Inf"} 1
inference_model_request_sizes_sum{model_name="m10",target_model_name="t11"} 2480
inference_model_request_sizes_count{model_name="m10",target_model_name="t11"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="64"} 0
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="128"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="256"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="512"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="1024"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="2048"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="4096"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="8192"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="16384"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="32768"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="65536"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="131072"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="262144"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="524288"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="1.048576e+06"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="2.097152e+06"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="4.194304e+06"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="8.388608e+06"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="1.6777216e+07"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="3.3554432e+07"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="6.7108864e+07"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="1.34217728e+08"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="2.68435456e+08"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="5.36870912e+08"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="1.073741824e+09"} 1
inference_model_request_sizes_bucket{model_name="m20",target_model_name="t20",le="+Inf"} 1
inference_model_request_sizes_sum{model_name="m20",target_model_name="t20"} 80
inference_model_request_sizes_count{model_name="m20",target_model_name="t20"} 1

# HELP inference_model_input_tokens [ALPHA] Inference model input token count distribution for requests in each model.
# TYPE inference_model_input_tokens histogram
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="1"} 0
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="8"} 0
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="16"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="32"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="64"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="128"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="256"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="512"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="1024"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="2048"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="4096"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="8192"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="16384"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="32778"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="65536"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="131072"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="262144"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="524288"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="1.048576e+06"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t10",le="+Inf"} 2
inference_model_input_tokens_sum{model_name="m10",target_model_name="t10"} 30
inference_model_input_tokens_count{model_name="m10",target_model_name="t10"} 2
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="1"} 0
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="8"} 0
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="16"} 0
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="32"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="64"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="128"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="256"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="512"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="1024"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="2048"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="4096"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="8192"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="16384"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="32778"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="65536"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="131072"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="262144"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="524288"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="1.048576e+06"} 1
inference_model_input_tokens_bucket{model_name="m10",target_model_name="t11",le="+Inf"} 1
inference_model_input_tokens_sum{model_name="m10",target_model_name="t11"} 30
inference_model_input_tokens_count{model_name="m10",target_model_name="t11"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="1"} 0
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="8"} 0
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="16"} 0
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="32"} 0
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="64"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="128"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="256"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="512"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="1024"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="2048"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="4096"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="8192"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="16384"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="32778"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="65536"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="131072"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="262144"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="524288"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="1.048576e+06"} 1
inference_model_input_tokens_bucket{model_name="m20",target_model_name="t20",le="+Inf"} 1
inference_model_input_tokens_sum{model_name="m20",target_model_name="t20"} 40
inference_model_input_tokens_count{model_name="m20",target_model_name="t20"} 1

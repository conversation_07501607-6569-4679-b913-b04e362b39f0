# HELP inference_model_output_tokens [ALPHA] Inference model output token count distribution for requests in each model.
# TYPE inference_model_output_tokens histogram
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="1"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="8"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="16"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="32"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="64"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="128"} 1
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="256"} 2
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="512"} 2
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="1024"} 2
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="2048"} 2
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="4096"} 2
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="8192"} 2
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t10",le="+Inf"} 2
inference_model_output_tokens_sum{model_name="m10",target_model_name="t10"} 300
inference_model_output_tokens_count{model_name="m10",target_model_name="t10"} 2
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="1"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="8"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="16"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="32"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="64"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="128"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="256"} 0
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="512"} 1
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="1024"} 1
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="2048"} 1
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="4096"} 1
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="8192"} 1
inference_model_output_tokens_bucket{model_name="m10",target_model_name="t11",le="+Inf"} 1
inference_model_output_tokens_sum{model_name="m10",target_model_name="t11"} 300
inference_model_output_tokens_count{model_name="m10",target_model_name="t11"} 1
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="1"} 0
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="8"} 0
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="16"} 0
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="32"} 0
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="64"} 0
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="128"} 0
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="256"} 0
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="512"} 1
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="1024"} 1
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="2048"} 1
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="4096"} 1
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="8192"} 1
inference_model_output_tokens_bucket{model_name="m20",target_model_name="t20",le="+Inf"} 1
inference_model_output_tokens_sum{model_name="m20",target_model_name="t20"} 400
inference_model_output_tokens_count{model_name="m20",target_model_name="t20"} 1

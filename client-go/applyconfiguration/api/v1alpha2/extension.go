/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha2

import (
	apiv1alpha2 "sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2"
)

// ExtensionApplyConfiguration represents a declarative configuration of the Extension type for use
// with apply.
type ExtensionApplyConfiguration struct {
	ExtensionReferenceApplyConfiguration  `json:",inline"`
	ExtensionConnectionApplyConfiguration `json:",inline"`
}

// ExtensionApplyConfiguration constructs a declarative configuration of the Extension type for use with
// apply.
func Extension() *ExtensionApplyConfiguration {
	return &ExtensionApplyConfiguration{}
}

// WithGroup sets the Group field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Group field is set to the value of the last call.
func (b *ExtensionApplyConfiguration) WithGroup(value apiv1alpha2.Group) *ExtensionApplyConfiguration {
	b.ExtensionReferenceApplyConfiguration.Group = &value
	return b
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *ExtensionApplyConfiguration) WithKind(value apiv1alpha2.Kind) *ExtensionApplyConfiguration {
	b.ExtensionReferenceApplyConfiguration.Kind = &value
	return b
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *ExtensionApplyConfiguration) WithName(value apiv1alpha2.ObjectName) *ExtensionApplyConfiguration {
	b.ExtensionReferenceApplyConfiguration.Name = &value
	return b
}

// WithPortNumber sets the PortNumber field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PortNumber field is set to the value of the last call.
func (b *ExtensionApplyConfiguration) WithPortNumber(value apiv1alpha2.PortNumber) *ExtensionApplyConfiguration {
	b.ExtensionReferenceApplyConfiguration.PortNumber = &value
	return b
}

// WithFailureMode sets the FailureMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the FailureMode field is set to the value of the last call.
func (b *ExtensionApplyConfiguration) WithFailureMode(value apiv1alpha2.ExtensionFailureMode) *ExtensionApplyConfiguration {
	b.ExtensionConnectionApplyConfiguration.FailureMode = &value
	return b
}

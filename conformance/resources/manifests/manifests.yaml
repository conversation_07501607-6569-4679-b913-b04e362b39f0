# Base Kubernetes resources for the Gateway API Inference Extension conformance tests.
# This includes namespaces and a minimal set of resources (Gateway, Backend)
# required by many tests. More specific resources should be defined within
# individual test files or other resource directories (e.g., sample_backends).

---
apiVersion: v1
kind: Namespace
metadata:
  name: gateway-conformance-infra
  labels:
    gateway-conformance: infra
---
apiVersion: v1
kind: Namespace
metadata:
  name: gateway-conformance-app-backend
  labels:
    gateway-conformance: backend
---
# A basic Gateway resource that allows HTTPRoutes from the same namespace.
# Tests can use this as a parent reference for routes that target InferencePools.
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: conformance-primary-gateway
  namespace: gateway-conformance-infra
spec:
  gatewayClassName: "{GATEWAY_CLASS_NAME}"
  listeners:
  - name: http
    port: 80
    protocol: HTTP
    allowedRoutes:
      namespaces:
        from: All
      kinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: conformance-secondary-gateway
  namespace: gateway-conformance-infra
spec:
  gatewayClassName: "{GATEWAY_CLASS_NAME}"
  listeners:
  - name: http
    port: 80
    protocol: HTTP
    hostname: "secondary.example.com"
    allowedRoutes:
      namespaces:
        from: All

### The following defines the essential resources for the gateway conformance test.
### All resources are created in the 'gateway-conformance-app-backend' namespace.
---
# Deploys a mock backend service to act as a model server.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: primary-inference-model-server-deployment
  namespace: gateway-conformance-app-backend
  labels:
    app: primary-inference-model-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: primary-inference-model-server
  template:
    metadata:
      labels:
        app: primary-inference-model-server
    spec:
      containers:
      - name: echoserver
        image: gcr.io/k8s-staging-gateway-api/echo-basic:v20240412-v1.0.0-394-g40c666fd
        ports:
        - containerPort: 3000
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 3
          periodSeconds: 5
          failureThreshold: 2
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
---
# Deploys a secondary mock backend service to act as a model server.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: secondary-inference-model-server-deployment
  namespace: gateway-conformance-app-backend
  labels:
    app: secondary-inference-model-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: secondary-inference-model-server
  template:
    metadata:
      labels:
        app: secondary-inference-model-server
    spec:
      containers:
      - name: echoserver
        image: gcr.io/k8s-staging-gateway-api/echo-basic:v20240412-v1.0.0-394-g40c666fd
        ports:
        - containerPort: 3000
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 3
          periodSeconds: 5
          failureThreshold: 2
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
---
# --- Primary InferencePool Definition ---
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: primary-inference-pool
  namespace: gateway-conformance-app-backend
spec:
  selector:
    app: primary-inference-model-server
  targetPortNumber: 3000
  extensionRef:
    name: primary-endpoint-picker-svc
---
# --- Primary Conformance EPP service Definition ---
apiVersion: v1
kind: Service
metadata:
  name: primary-endpoint-picker-svc
  namespace: gateway-conformance-app-backend
spec:
  selector:
    app: primary-app-backend-epp
  ports:
    - protocol: TCP
      port: 9002
      targetPort: 9002
      appProtocol: http2
  type: ClusterIP
---
# --- Primary Conformance EPP Deployment ---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: primary-app-endpoint-picker
  namespace: gateway-conformance-app-backend
  labels:
    app: primary-app-backend-epp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: primary-app-backend-epp
  template:
    metadata:
      labels:
        app: primary-app-backend-epp
    spec:
      # Conservatively, this timeout should mirror the longest grace period of the pods within the pool
      terminationGracePeriodSeconds: 130
      containers:
      - name: epp
        image: us-central1-docker.pkg.dev/k8s-staging-images/gateway-api-inference-extension/epp:main
        imagePullPolicy: Always
        args:
        - -poolName
        - "primary-inference-pool"
        - -poolNamespace
        - "gateway-conformance-app-backend"
        - -v
        - "4"
        - --zap-encoder
        - "json"
        - -grpcPort
        - "9002"
        - -grpcHealthPort
        - "9003"
        - "-configFile"
        - "/config/conformance-plugins.yaml"
        ports:
        - containerPort: 9002
        - containerPort: 9003
        - name: metrics
          containerPort: 9090
        livenessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
        readinessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
        volumeMounts:
        - name: plugins-config-volume
          mountPath: "/config"
      volumes:
      - name: plugins-config-volume
        configMap:
          name: plugins-config
---
# --- Secondary InferencePool Definition ---
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: secondary-inference-pool
  namespace: gateway-conformance-app-backend
spec:
  selector:
    app: secondary-inference-model-server
  targetPortNumber: 3000
  extensionRef:
    name: secondary-endpoint-picker-svc
    failureMode: FailOpen
---
# --- Secondary Conformance EPP service Definition ---
apiVersion: v1
kind: Service
metadata:
  name: secondary-endpoint-picker-svc
  namespace: gateway-conformance-app-backend
spec:
  selector:
    app: secondary-app-backend-epp
  ports:
    - protocol: TCP
      port: 9002
      targetPort: 9002
      appProtocol: http2
  type: ClusterIP
---
# --- Secondary Conformance EPP Deployment ---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: secondary-app-endpoint-picker
  namespace: gateway-conformance-app-backend
  labels:
    app: secondary-app-backend-epp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: secondary-app-backend-epp
  template:
    metadata:
      labels:
        app: secondary-app-backend-epp
    spec:
      # Conservatively, this timeout should mirror the longest grace period of the pods within the pool
      terminationGracePeriodSeconds: 130
      containers:
      - name: epp
        image: us-central1-docker.pkg.dev/k8s-staging-images/gateway-api-inference-extension/epp:main
        imagePullPolicy: Always
        args:
        - -poolName
        - "secondary-inference-pool"
        - -poolNamespace
        - "gateway-conformance-app-backend"
        - -v
        - "4"
        - --zap-encoder
        - "json"
        - -grpcPort
        - "9002"
        - -grpcHealthPort
        - "9003"
        - "-configFile"
        - "/config/conformance-plugins.yaml"
        ports:
        - containerPort: 9002
        - containerPort: 9003
        - name: metrics
          containerPort: 9090
        livenessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
        readinessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
        volumeMounts:
        - name: plugins-config-volume
          mountPath: "/config"
      volumes:
      - name: plugins-config-volume
        configMap:
          name: plugins-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: plugins-config
  namespace: gateway-conformance-app-backend
data:
  conformance-plugins.yaml: |
    apiVersion: inference.networking.x-k8s.io/v1alpha1
    kind: EndpointPickerConfig
    plugins:
    - type: header-based-testing-filter
    - type: max-score-picker
      parameters:
        maxNumOfEndpoints: 1
    - type: single-profile-handler
    schedulingProfiles:
    - name: conformance-profile
      plugins:
      - pluginRef: header-based-testing-filter
      - pluginRef: max-score-picker
---
# --- Required Role and RoleBinding for Conformance Test for EPP ---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: inference-model-reader
  namespace: gateway-conformance-app-backend
rules:
- apiGroups: ["inference.networking.x-k8s.io"]
  resources: ["inferencemodels", "inferencepools"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: epp-to-inference-model-reader
  namespace: gateway-conformance-app-backend
subjects:
- kind: ServiceAccount
  name: default
  namespace: gateway-conformance-app-backend
roleRef:
  kind: Role
  name: inference-model-reader
  apiGroup: rbac.authorization.k8s.io

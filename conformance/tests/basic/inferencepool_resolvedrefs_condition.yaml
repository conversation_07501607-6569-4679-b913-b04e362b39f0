# conformance/tests/basic/inferencepool_resolvedrefs_condition.yaml

# This manifest defines the initial resources for the
# inferencepool_resolvedrefs_condition.go conformance test.

# --- HTTPRoute for Primary Gateway (conformance-primary-gateway) ---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-for-primary-gw
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: conformance-primary-gateway
    namespace: gateway-conformance-infra
    sectionName: http
  hostnames:
  - "primary.example.com"
  rules:
  - backendRefs:
    - group: inference.networking.x-k8s.io
      kind: InferencePool
      name: primary-inference-pool
    matches:
    - path:
        type: PathPrefix
        value: /primary-gateway-test
---
# --- HTTPRoute for Secondary Gateway (conformance-secondary-gateway) ---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-for-secondary-gw
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: conformance-secondary-gateway
    namespace: gateway-conformance-infra
    sectionName: http
  hostnames:
  - "secondary.example.com"
  rules:
  - backendRefs:
    - group: inference.networking.x-k8s.io
      kind: InferencePool
      name: primary-inference-pool
    matches:
    - path:
        type: PathPrefix
        value: /secondary-gateway-test

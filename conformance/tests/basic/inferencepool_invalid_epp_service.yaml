apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: pool-with-invalid-epp
  namespace: gateway-conformance-app-backend
spec:
  selector:
    app: primary-inference-model-server
  targetPortNumber: 3000
  extensionRef:
    name: non-existent-epp-svc
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-for-invalid-epp-pool
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
  - name: conformance-primary-gateway
    namespace: gateway-conformance-infra
  rules:
  - backendRefs:
    - name: pool-with-invalid-epp
      kind: InferencePool
      group: inference.networking.x-k8s.io
    matches:
    - path:
        type: PathPrefix
        value: /invalid-epp-test

# --- HTTPRoute Definition ---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-for-inferencepool-accepted
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: conformance-primary-gateway # Name of the shared Gateway from maniffests.yaml
    namespace: gateway-conformance-infra  # Namespace of the shared Gateway
    sectionName: http
  rules:
  - backendRefs:
    - group: inference.networking.x-k8s.io # InferencePool API group
      kind: InferencePool
      name: primary-inference-pool # Name of the InferencePool this route points to
      # namespace: gateway-conformance-app-backend - is omitted since it is in the same namespace as HTTPRoute
    matches:
    - path:
        type: PathPrefix
        value: /accepted-pool-test

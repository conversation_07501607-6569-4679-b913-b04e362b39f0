---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-multiple-rules-different-pools
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
    - name: conformance-primary-gateway
      namespace: gateway-conformance-infra
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /primary
      backendRefs:
        - name: primary-inference-pool
          kind: InferencePool
          group: inference.networking.x-k8s.io
    - matches:
        - path:
            type: PathPrefix
            value: /secondary
      backendRefs:
        - name: secondary-inference-pool
          kind: InferencePool
          group: inference.networking.x-k8s.io

# httproute_invalid_inferencepool_ref.yaml
# This manifest defines an HTTPRoute that references an InferencePool
# by name ("non-existent-inference-pool") which is intentionally NOT defined.
# The test will verify that the HTTPRoute reflects an appropriate
# failure status because the referenced InferencePool backend cannot be found.

apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  # This name must match the 'routeNN.Name' in the Go test file.
  name: httproute-to-non-existent-pool
  # This namespace should be one created by the base manifests,
  # typically where backend applications and their routes reside.
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: conformance-primary-gateway # Name of the shared Gateway from base manifests
    namespace: gateway-conformance-infra  # Namespace of the shared Gateway
    sectionName: http
  rules:
  - backendRefs:
    - group: inference.networking.x-k8s.io
      kind: InferencePool
      name: non-existent-inference-pool # Intentionally Non-Existing
    matches:
    - path:
        type: PathPrefix
        value: /test-non-existent-pool

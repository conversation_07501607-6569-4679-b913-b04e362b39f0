---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-for-primary-gateway
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
  - kind: Gateway
    name: conformance-primary-gateway
    namespace: gateway-conformance-infra
  hostnames:
  - "primary.example.com"
  rules:
  - backendRefs:
    - group: inference.networking.x-k8s.io
      kind: InferencePool
      name: primary-inference-pool
    matches:
    - path:
        type: PathPrefix
        value: /test-primary-gateway
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-for-secondary-gateway
  namespace: gateway-conformance-app-backend
spec:
  parentRefs:
  - kind: Gateway
    name: conformance-secondary-gateway
    namespace: gateway-conformance-infra
  hostnames:
  - "secondary.example.com"
  rules:
  - backendRefs:
    - group: inference.networking.x-k8s.io
      kind: InferencePool
      name: secondary-inference-pool
    matches:
    - path:
        type: PathPrefix
        value: /test-secondary-gateway

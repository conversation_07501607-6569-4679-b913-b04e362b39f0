# See https://cloud.google.com/cloud-build/docs/build-config
timeout: 3000s
# A build step specifies an action that you want Prow to perform.
# For each build step, Prow executes a job.
steps:
# see https://github.com/kubernetes/test-infra/tree/master/config/jobs/image-pushing
  - name: gcr.io/k8s-staging-test-infra/gcb-docker-gcloud:v20240718-5ef92b5c36
    entrypoint: make
    args:
      - image-push
    env:
    - GIT_TAG=$_GIT_TAG
    - EXTRA_TAG=$_PULL_BASE_REF
    - DOCKER_BUILDX_CMD=/buildx-entrypoint
    - GIT_COMMIT_SHA=$_PULL_BASE_SHA
  - name: gcr.io/k8s-staging-test-infra/gcb-docker-gcloud:v20240718-5ef92b5c36
    entrypoint: make
    args:
      - syncer-image-push
    env:
    - GIT_TAG=$_GIT_TAG
    - EXTRA_TAG=$_PULL_BASE_REF
    - DOCKER_BUILDX_CMD=/buildx-entrypoint
  - name: gcr.io/k8s-staging-test-infra/gcb-docker-gcloud:v20240718-5ef92b5c36
    entrypoint: make
    args:
      - inferencepool-helm-chart-push
      - bbr-helm-chart-push
    env:
    - EXTRA_TAG=$_PULL_BASE_REF
    - GOTOOLCHAIN=auto
  - name: gcr.io/k8s-staging-test-infra/gcb-docker-gcloud:v20240718-5ef92b5c36
    entrypoint: make
    args:
      - bbr-image-push
    env:
    - GIT_TAG=$_GIT_TAG
    - EXTRA_TAG=$_PULL_BASE_REF
    - DOCKER_BUILDX_CMD=/buildx-entrypoint
substitutions:
  # _GIT_TAG will be filled with a git-based tag for the image, of the form vYYYYMMDD-hash, and
  # can be used as a substitution
  _GIT_TAG: '0.0.0'
  # _PULL_BASE_REF will contain the ref that was pushed to trigger this build -
  # a branch like 'main' or 'release-0.2', or a tag like 'v0.2'.
  _PULL_BASE_REF: 'main'
  # _PULL_BASE_SHA will contain the Git SHA of the commit that was pushed to trigger this build.
  _PULL_BASE_SHA: 'abcdef'
options:
  substitution_option: ALLOW_LOOSE

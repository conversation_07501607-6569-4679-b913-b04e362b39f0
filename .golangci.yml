run:
  timeout: 5m
  allow-parallel-runners: true

# Settings related to issues
issues:
  # Which dirs to exclude: issues from them won't be reported
  exclude-dirs:
    - bin
linters:
  disable-all: true
  enable:
    - copyloopvar
    - dupword
    - durationcheck
    - fatcontext
    - ginkgolinter
    - gocritic
    - govet
    - loggercheck
    - misspell
    - perfsprint
    - revive
    - unconvert
    - makezero
    - errcheck
    - goconst
    - gofmt
    - goimports
    - gosimple
    - ineffassign
    - nakedret
    - prealloc
    - typecheck
    - unparam
    - unused
    
linters-settings:
  revive:
    rules:
      - name: comment-spacings

title: vLLMLoRAConfig
description: Configuration for vLLM LoRA adapters
type: object
properties:
  vLLMLoRAConfig:
    type: object
    properties:
      host:
        type: string
        description: Model server's host
        default: localhost
      port:
        type: integer
        description: Model server's port
        default: 8000
      name:
        type: string
        description: Name of this config
      defaultBaseModel:
        type: string
        description: Default base model to use when not specified at adapter level
      ensureExist:
        type: object
        description: List of models to ensure existence on specified model server
        properties:
          models:
            type: array
            description: List of LoRA adapter configurations
            items:
              type: object
              properties:
                base-model:
                  type: string
                  description: Base model for LoRA adapter (overrides defaultBaseModel)
                id:
                  type: string
                  description: Unique ID of LoRA adapter
                source:
                  type: string
                  description: Path (remote or local) to LoRA adapter
              required:
                - id
                - source
        required:
          - models
      ensureNotExist:
        type: object
        description: List of models to ensure non-existence on specified model server
        properties:
          models:
            type: array
            description: List of LoRA adapter configurations
            items:
              type: object
              properties:
                base-model:
                  type: string
                  description: Base model for LoRA adapter (overrides defaultBaseModel)
                id:
                  type: string
                  description: Unique ID of LoRA adapter
                source:
                  type: string
                  description: Path (remote or local) to LoRA adapter
              required:
                - id
                - source
        required:
          - models
required:
  - vLLMLoRAConfig
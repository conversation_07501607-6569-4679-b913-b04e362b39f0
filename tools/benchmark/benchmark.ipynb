{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 391, "status": "ok", "timestamp": 1741734317446, "user": {"displayName": "<PERSON><PERSON>", "userId": "18222691451061354557"}, "user_tz": 420}, "id": "ziJD5zt0c1Rt"}, "outputs": [], "source": ["#@title Configuration. Edit this before running the rest.\n", "\n", "OUTPUT_DIR='output'\n", "RUN_ID='example-run'\n", "# Path to the benchmark dir under `gateway-api-inference-extension/benchmark`\n", "BENCHMARK_DIR =\"./\"\n", "# A regex to match the model name, which matches the output file name.\n", "MODEL_MATCHER='.*llama.*'\n", "INTERACTIVE_PLOT='False'"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 33, "status": "ok", "timestamp": 1741735749209, "user": {"displayName": "<PERSON><PERSON>", "userId": "18222691451061354557"}, "user_tz": 420}, "id": "dB7xALgLawN-"}, "outputs": [], "source": ["#@title Plot Helper\n", "import os\n", "import pandas as pd\n", "import re\n", "import json\n", "from collections import OrderedDict\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import math\n", "from sklearn.metrics import r2_score\n", "import logging\n", "level = logging.INFO\n", "logger = logging.getLogger(__name__)\n", "logger.setLevel(level)\n", "handler = logging.StreamHandler()  # This sends output to the console\n", "handler.setLevel(level) # Set handler level\n", "logger.add<PERSON><PERSON><PERSON>(handler)\n", "\n", "title_fontsize = 18\n", "axis_label_fontsize = 18\n", "legend_fontsize = 16\n", "tick_label_fontsize = 14\n", "\n", "# Encapsulates some basic information needed to plot metrics.\n", "class XY:\n", "  def __init__(self, x: str, y: str, x_label=None, y_label=None):\n", "    self.x = x\n", "    self.y = y\n", "    self.x_label = x if x_label is None else x_label\n", "    self.y_label = y if y_label is None else y_label\n", "\n", "NUM_PLOTS_PER_ROW = 4\n", "# The arguments need to match the metric name fields generated by the benchmark tool.\n", "CORE_METRICS = [\n", "    XY(x = 'request_rate', x_label = 'QPS', y = 'output_tokens_per_min'),\n", "    XY(x = \"request_rate\", x_label = 'QPS', y = \"p90_per_output_token_latency\"),\n", "    XY(x = \"request_rate\", x_label = 'QPS', y = \"p90_latency\"),\n", "    XY(x = \"request_rate\", x_label = 'QPS', y=\"num_prompts_attempted\"),\n", "    XY(x = \"request_rate\", x_label = 'QPS', y=\"num_prompts_succeeded\"),\n", "]\n", "SANITY_CHECK_METRICS = [\n", "    XY(x = 'request_rate', x_label = 'QPS', y = 'benchmark_time'),\n", "    XY(x = 'request_rate', x_label = 'QPS', y = 'throughput_rps'),\n", "    XY(x = 'request_rate', x_label = 'QPS', y = 'total_input_tokens'),\n", "    XY(x = 'request_rate', x_label = 'QPS', y = 'total_output_token'),\n", "    XY(x = 'request_rate', x_label = 'QPS', y = 'avg_input_len'),\n", "    XY(x = 'request_rate', x_label = 'QPS', y = 'avg_output_len'),\n", "]\n", "\n", "class Label:\n", "  def __init__(self, name, alias=None):\n", "    self.name = name\n", "    self.alias = name if alias is None else alias\n", "\n", "ALL_METRICS = CORE_METRICS  + SANITY_CHECK_METRICS\n", "\n", "class Plotter:\n", "  def __init__(self, run_id, labels=None, metrics=CORE_METRICS, num_plots_per_row=5, interactive=False, annotate=False, output_dir=OUTPUT_DIR):\n", "    self.run_id = run_id\n", "    self.labels = labels\n", "    self.metrics = metrics\n", "    self.num_plots_per_row = num_plots_per_row\n", "    self.interactive = interactive\n", "    self.annotate = annotate\n", "    self.output_dir = output_dir\n", "    self.data = load_data(self.labels, self.run_id, self.output_dir)\n", "    self.groups = group_data(self.data, self.metrics)\n", "\n", "  def withRunId(self, run_id):\n", "    return Plotter(run_id, self.labels, self.metrics, self.num_plots_per_row, self.interactive, self.annotate, self.output_dir)\n", "\n", "  def with<PERSON><PERSON><PERSON>(self, labels):\n", "    return Plotter(self.run_id, labels, self.metrics, self.num_plots_per_row, self.interactive, self.annotate, self.output_dir)\n", "\n", "  def withMetrics(self, metrics):\n", "    return Plotter(self.run_id, self.labels, metrics, self.num_plots_per_row, self.interactive, self.annotate, self.output_dir)\n", "\n", "  def withOutputDir(self, output_dir):\n", "    return Plotter(self.run_id, self.labels, self.metrics, self.num_plots_per_row, self.interactive, self.annotate, output_dir)\n", "\n", "  def plot_bar(self):\n", "    \n", "    logger.debug(\"Plotting run id...\")\n", "    plot_bar(self.labels, self.groups, self.metrics, self.num_plots_per_row, self.interactive, annotate=self.annotate)\n", "\n", "  def plot_delta(self):\n", "    \"\"\"\n", "    Plot the delta between two labels.\n", "    \"\"\"\n", "    logger.debug(\"Plotting delta for run id...\")\n", "    plot_delta(self.labels, self.groups, self.metrics, self.num_plots_per_row, self.interactive, annotate=self.annotate)\n", "\n", "def filepaths(root_dir):\n", "    \"\"\"\n", "    Recursively reads files within a directory and returns a list of file paths.\n", "    \"\"\"\n", "\n", "    filepaths = []\n", "    for dirpath, dirnames, filenames in os.walk(root_dir):\n", "        for filename in filenames:\n", "            filepath = os.path.join(dirpath, filename)\n", "            filepaths.append(filepath)\n", "    return filepaths\n", "\n", "def flatten_server_metrics(server_metrics):\n", "  \"\"\"\n", "  Flattens the server metrics json to a single level.\n", "  \"\"\"\n", "  flattend = {}\n", "  for k, v in server_metrics.items():\n", "    if isinstance(v, dict):\n", "      for k2, v2 in v.items():\n", "        flattend[k + \".\" + k2] = v2\n", "\n", "  return flattend\n", "\n", "def load_data(labels, run_id, output_dir=OUTPUT_DIR):\n", "  data_path =f\"{BENCHMARK_DIR}/{output_dir}/{run_id}\"\n", "  records = []\n", "  logger.debug(f\"Loading data for {data_path}\")\n", "  for file in filepaths(data_path):\n", "    for label in labels:\n", "      regex = f\".*\\/{label.name}\\/results/json/{MODEL_MATCHER}.json\"\n", "      logger.debug(f\"matching file {file} for regex {regex} and label {label}\")\n", "      if re.match(regex, file):\n", "        logger.debug(f\"found match file {file} for regex {regex} and label {label}\")\n", "        with open(file, 'r') as f:\n", "          raw_data = json.load(f)\n", "          sample_data = {\n", "              'file_name': f.name,\n", "              'label': label.alias,\n", "              **raw_data.get(\"metrics\",{}),\n", "              **flatten_server_metrics(raw_data.get(\"metrics\",{}).get(\"server_metrics\", {})),\n", "          }\n", "          sample_data['request_rate'] = sample_data['request_rate'] * raw_data['config']['num_models']\n", "          records.append(sample_data)\n", "  all_data = pd.DataFrame.from_records(records, index='file_name') if len(records) > 0 else pd.DataFrame()\n", "  return all_data\n", "\n", "def group_data(all_data, metrics=CORE_METRICS):\n", "  try:\n", "    data = all_data.sort_values(by=['request_rate'], ascending=True).copy().dropna()\n", "  except:\n", "    # print(\"No data found\")\n", "    return None\n", "\n", "  # Ensure there is exactly one benchmark result per label and x-axis for each\n", "  # metric.\n", "  x_axes = set()\n", "  for m in metrics:\n", "    x_axes.add(m.x)\n", "\n", "  for x in x_axes:\n", "    sizes = data.groupby(by=['label', x], dropna=True).size()\n", "    for index, v in sizes.items():\n", "      if v > 1:\n", "        label, _ = index\n", "        # print(f\"Multiple benchmark results for the same label ({label}), and x-axis ({x}). {index}: {v}. Please use more selective file filters.\")\n", "        # raise ValueError(f\"Multiple benchmark results for the same label ({label}), and x-axis ({x}). Please use more selective file filters.\")\n", "\n", "  # Group by label.\n", "  groups = data.groupby(by=['label'],sort=True)\n", "  return groups\n", "\n", "def compute_r2_for_metrics(groups, metrics, label_before, label_after):\n", "    print(\"\\nCoefficient of Determination (R^2) between before and after runs:\")\n", "    for m in metrics:\n", "        try:\n", "            df_b = groups.get_group(label_before).set_index('request_rate')\n", "            df_a = groups.get_group(label_after).set_index('request_rate')\n", "        except KeyError:\n", "            print(f\"  Skipping {m.y}: missing group data for '{label_before}' or '{label_after}'\")\n", "            continue\n", "        common = sorted(set(df_b.index).intersection(df_a.index))\n", "        yb = df_b.loc[common, m.y].values\n", "        ya = df_a.loc[common, m.y].values\n", "        mask = ~np.isnan(yb) & ~np.isnan(ya)\n", "        yb, ya = yb[mask], ya[mask]\n", "        if len(yb) > 1 and np.any(yb != 0):\n", "            r2 = r2_score(yb, ya)\n", "            print(f\"  {m.y:<30} R^2 = {r2:.4f}\")\n", "        else:\n", "            print(f\"  {m.y:<30} insufficient data for R^2 calculation\")\n", "\n", "\n", "def init_plot(metrics, num_plots_per_row=NUM_PLOTS_PER_ROW):\n", "  num_plots_per_row = min(num_plots_per_row, len(metrics))\n", "  row_count = math.ceil(len(metrics) / num_plots_per_row)\n", "  fig, axes = plt.subplots(nrows=row_count, ncols=num_plots_per_row, figsize=(20, 5*row_count), tight_layout=True)\n", "  if row_count == 1 and num_plots_per_row == 1:\n", "    axes = [axes]\n", "  return fig, axes\n", "\n", "def plot_metrics(metrics, plot_func, num_plots_per_row=NUM_PLOTS_PER_ROW, fig=None, axes=None):\n", "  \"\"\"\n", "  plot_func: a function in the form of def plot_func(ax:~matplotlib.axes.Axes , m: XY):\n", "  \"\"\"\n", "  logger.debug(f'Plotting metrics: {metrics}')\n", "  num_plots_per_row = min(num_plots_per_row, len(metrics))\n", "  if fig is None or axes is None:\n", "    logger.debug(f'Creating new figure and axes')\n", "    fig, axes = init_plot(metrics, num_plots_per_row)\n", "  row_count = math.ceil(len(metrics) / num_plots_per_row)\n", "  for i, m in enumerate(metrics):\n", "    row = math.floor(i/num_plots_per_row)\n", "    col = i%num_plots_per_row\n", "    if row_count == 1:\n", "      curAx = axes[col]\n", "    else:\n", "      curAx = axes[row, col]\n", "    plot_func(curAx, m)\n", "  return fig, axes\n", "\n", "def plot_bar(labels, groups, metrics=CORE_METRICS, num_plots_per_row=NUM_PLOTS_PER_ROW, interactive=INTERACTIVE_PLOT, annotate=False):\n", "    labels = [label.alias for label in labels]\n", "    logger.debug(f'Prnting bar chart for {labels}')\n", "    logger.debug(f'groups: {groups}')\n", "    dataframes = []\n", "    for label in labels:\n", "      try:\n", "        dataframes.append(groups.get_group((label,)))\n", "      except:\n", "        logger.debug(f\"No data found for label {label}\")\n", "        continue\n", "    y_columns = [m.y for m in metrics]\n", "    logger.debug(f'y_columns: {y_columns}')\n", "    logger.debug(f'dataframes: {dataframes}')\n", "\n", "    # 1. Combine all request rates\n", "    all_request_rates = set()\n", "    for df in dataframes:\n", "        all_request_rates.update(df['request_rate'].astype(int))\n", "    all_request_rates = sorted(list(all_request_rates))\n", "\n", "    # 2. Prepare data for plotting:  Create a nested dictionary\n", "    plot_data = {y_col: {label: {} for label in labels} for y_col in y_columns}\n", "\n", "    for i, df in enumerate(dataframes):\n", "        label = labels[i]\n", "        df_dict = df.set_index('request_rate').to_dict()\n", "        for y_col in y_columns:\n", "            for request_rate in all_request_rates:\n", "                plot_data[y_col][label][request_rate] = df_dict.get(y_col, {}).get(request_rate, np.nan)\n", "\n", "    logger.debug(f'Plot_data: {plot_data}')\n", "\n", "    # 3. <PERSON><PERSON><PERSON>\n", "    def plot_func(curAx, m):\n", "      num_request_rates = len(all_request_rates)\n", "      num_labels = len(labels)\n", "      x = np.arange(num_request_rates)  # the label locations (x-axis positions)\n", "      width = 0.4 / num_labels   # width of the bars\n", "\n", "      for i, label in enumerate(labels):\n", "          bar_x = x - (width*num_labels)/2 + i*width + width/2\n", "          #Extract y-values to plot\n", "          y_values = [plot_data[m.y][label][rr] for rr in all_request_rates]\n", "\n", "          rects = curAx.bar(bar_x, y_values, width, label=label)\n", "          if annotate:\n", "            for rect, val in zip(rects, y_values):\n", "                if not np.isnan(val):\n", "                    height = rect.get_height()\n", "                    curAx.annotate(f'{val:.2f}',\n", "                            xy=(rect.get_x() + rect.get_width() / 2, height),\n", "                            xytext=(0, 3),  # 3 points vertical offset\n", "                            textcoords=\"offset points\",\n", "                            ha='center', va='bottom')\n", "      # Add labels, title, and legend\n", "      curAx.set_xlabel(m.x_label, fontsize=axis_label_fontsize)\n", "      curAx.set_ylabel(m.y_label, fontsize=axis_label_fontsize)\n", "      curAx.set_xticks(x)\n", "      curAx.set_xticklabels(all_request_rates)\n", "      curAx.tick_params(axis='both', labelsize=tick_label_fontsize)\n", "      curAx.legend(fontsize=legend_fontsize, loc='upper left', frameon=True, framealpha=0.8, edgecolor='black')\n", "    fig, axes = plot_metrics(metrics, plot_func, num_plots_per_row)\n", "    fig.tight_layout(rect=[0, 0.03, 1, 0.95])\n", "    plt.show()\n", "\n", "def plot_delta(labels, groups, metrics=CORE_METRICS, num_plots_per_row=NUM_PLOTS_PER_ROW, interactive=True, annotate=False):\n", "    \"\"\"\n", "    Plot the delta between base_label and compare_label for each metric.\n", "    A positive delta means compare_label has a higher value than base_label.\n", "    \"\"\"\n", "    base_label = labels[0].name\n", "    compare_label = labels[1].name\n", "    logger.debug(f'Printing delta chart for {base_label} vs {compare_label}')\n", "\n", "    try:\n", "        base_df = groups.get_group((base_label,))\n", "        compare_df = groups.get_group((compare_label,))\n", "    except Exception as e:\n", "        logger.error(f\"Error getting data for labels {base_label} and {compare_label}: {e}\")\n", "        return\n", "\n", "    y_columns = [m.y for m in metrics]\n", "\n", "    # 1. Find common request rates\n", "    base_rates = set(base_df['request_rate'].astype(int))\n", "    compare_rates = set(compare_df['request_rate'].astype(int))\n", "    common_rates = sorted(list(base_rates.intersection(compare_rates)))[:6]\n", "\n", "    if not common_rates:\n", "        logger.error(f\"No common request rates found between {base_label} and {compare_label}\")\n", "        return\n", "\n", "    # 2. Prepare data for delta calculation\n", "    base_data = base_df.set_index('request_rate').to_dict()\n", "    compare_data = compare_df.set_index('request_rate').to_dict()\n", "\n", "    # Calculate deltas (compare_label - base_label)\n", "    delta_data = {y_col: {} for y_col in y_columns}\n", "    for y_col in y_columns:\n", "        for rate in common_rates:\n", "            base_val = base_data.get(y_col, {}).get(rate, np.nan)\n", "            compare_val = compare_data.get(y_col, {}).get(rate, np.nan)\n", "\n", "            if not np.isnan(base_val) and not np.isnan(compare_val):\n", "                delta_data[y_col][rate] = (compare_val - base_val)/base_val*100\n", "            else:\n", "                delta_data[y_col][rate] = np.nan\n", "\n", "    # 3. <PERSON><PERSON><PERSON>\n", "    def plot_func(curAx, m):\n", "        x = np.arange(len(common_rates))\n", "        y_values = [delta_data[m.y].get(rr, np.nan) for rr in common_rates]\n", "\n", "        # Determine colors based on positive/negative values\n", "        colors = ['green' if val > 0 else 'blue' for val in y_values]\n", "\n", "        rects = curAx.bar(x, y_values, 0.6, color=colors)\n", "\n", "        # Add a horizontal line at y=0\n", "        curAx.axhline(y=0, color='black', linestyle='-', linewidth=1)\n", "\n", "        if annotate:\n", "            for rect, val in zip(rects, y_values):\n", "                if not np.isnan(val):\n", "                    height = rect.get_height()\n", "                    # For negative bars, put text above the bar\n", "                    vert_align = 'bottom' if val >= 0 else 'top'\n", "                    y_offset = 3 if val >= 0 else -3\n", "\n", "                    curAx.annotate(f'{val:.2f}',\n", "                            xy=(rect.get_x() + rect.get_width() / 2, val),\n", "                            xytext=(0, y_offset),  # vertical offset\n", "                            textcoords=\"offset points\",\n", "                            ha='center', va=vert_align)\n", "\n", "        # Create a title that shows what this delta represents\n", "        title = f\"Delta: {compare_label} - {base_label} ({m.y})\"\n", "        curAx.set_title(title, fontsize=12)\n", "\n", "        # Add labels\n", "        curAx.set_xlabel(m.x_label, fontsize=axis_label_fontsize)\n", "        #curAx.set_ylabel(f\"% Delta in {m.y_label}\", fontsize=axis_label_fontsize)\n", "        curAx.set_xticks(x)\n", "        curAx.set_xticklabels(common_rates)\n", "        curAx.tick_params(axis='both', labelsize=tick_label_fontsize)\n", "\n", "        # Create a dummy handle for the legend\n", "        legend_handle = [plt.Rectangle((0,0),1,1,color='green'),\n", "                        plt.Rectangle((0,0),1,1,color='blue')]\n", "        legend_label = [f'{compare_label} > {base_label}',\n", "                       f'{compare_label} < {base_label}']\n", "\n", "        return legend_handle, legend_label\n", "\n", "    # Create plot with metrics\n", "    fig, axes = plot_metrics(metrics, plot_func, num_plots_per_row)\n", "\n", "    # Add an overall title for the figure\n", "    fig.suptitle(f\"% Delta Metrics: {compare_label} - {base_label}\",\n", "                fontsize=title_fontsize, y=0.98)\n", "\n", "    plt.subplots_adjust(bottom=0.15, top=0.9)  # Make room for legends\n", "    fig.tight_layout(rect=[0, 0.1, 1, 0.95])  # Adjust the rectangle in which the subplots fit\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"height": 1000}, "executionInfo": {"elapsed": 2232, "status": "ok", "timestamp": 1741735855456, "user": {"displayName": "<PERSON><PERSON>", "userId": "18222691451061354557"}, "user_tz": 420}, "id": "HbGEAOucb_Jn", "outputId": "faf0304b-92f4-4fa7-ae71-83b8bd987e70"}, "outputs": [], "source": ["#@title Plot Result\n", "# initialize the plotter with the run id and labels. \n", "# Example labels are 'inference-extension' and 'k8s-svc' if comparing Inference Extension and K8s Service \n", "# 'regression-before' and 'regression-after' if comparing two different runs of inference extension to see the regression\n", "\n", "benchmark_id1 =  <ID1> # eg 'regression-before' or 'inference-extension'\n", "benchmark_id2 = <ID2> # eg 'regression-after' or 'k8s-svc'\n", "labels = [Label(benchmark_id1), Label(benchmark_id2,)]\n", "\n", "# Plot bar chart of metrics\n", "pl = Plotter(run_id=RUN_ID, labels=labels, output_dir=OUTPUT_DIR)\n", "pl.plot_bar()\n", "pl.plot_delta()\n", "\n", "# Load & group data to compute R^2\n", "all_data = load_data(labels, RUN_ID, OUTPUT_DIR)\n", "groups = group_data(all_data)\n", "compute_r2_for_metrics(groups, CORE_METRICS,\n", "                           label_before=benchmark_id1,\n", "                           label_after=benchmark_id2)\n", "\n"]}], "metadata": {"colab": {"last_runtime": {"build_target": "", "kind": "local"}, "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 0}
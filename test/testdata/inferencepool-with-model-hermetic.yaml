apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: vllm-llama3-8b-instruct-pool
  namespace: default
spec:
  targetPortNumber: 8000
  selector:
    app: vllm-llama3-8b-instruct-pool
  extensionRef:
    name: epp
---
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: sample
  namespace: default
spec:
  modelName: sql-lora
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct-pool
  targetModels:
  - name: sql-lora-1fdg2
    weight: 100
---
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: sheddable
  namespace: default
spec:
  modelName: sql-lora-sheddable
  poolRef:
    name: vllm-llama3-8b-instruct-pool
  targetModels:
  - name: sql-lora-1fdg3
    weight: 100
---
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: generic
  namespace: default
spec:
  modelName: my-model
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct-pool
  targetModels:
  - name: my-model-12345
    weight: 100    
---
apiVersion: inference.networking.x-k8s.io/v1alpha2
kind: InferenceModel
metadata:
  name: direct-model-name
  namespace: default
spec:
  modelName: direct-model
  criticality: Critical
  poolRef:
    name: vllm-llama3-8b-instruct-pool